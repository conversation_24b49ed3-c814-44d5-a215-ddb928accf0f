import React from "react";
import { useSelector } from "react-redux";


const NewPerformanceDashboard = () => {
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    return(
        <div className="bg-smoke font-lato">
            <div className="col-12">
                <div>
                    <div className="col-12 flex align-items-center">
                        <span className="text-big-one">
                            Hello
                            {login_data?.role === "clientadmin"
                                ? login_data?.information?.companyname
                                : login_data?.information?.empname}
                            !
                        </span>
                        <span className="ml-1">{`<${login_data.email}>`} </span>
                    </div>

                    <div
                        className="flex col-12 flex-start"
                        style={{ flexDirection: "column" }}
                    >
                        <span className="text-big-one">
                            Sustainability performance dashboard
                        </span>
                        <p className="ml-1">Detailed progress report of ESG Indicators</p>
                        {/* <Tag className="ml-3 p-tag-blue">
        
                      {login_data.role === "clientadmin"
                        ? "Enterprise Admin"
                        : getRoles(login_data.information)}
                    </Tag> */}
                    </div>

                    <div
                        style={{width:'100%',height:'100%',       justifyContent: "center",display:'flex'}}
                       
                    >
                               <iframe style={{width: '100%', height: '100vh',border:'none'}} src={'https://esg-enterprise-admin.web.app'} />

                        {/* <div style="width: 100%; height: 100%;">
  <iframe 
    src="https://example.com"
    style="width: 100%; height: 100%; border: none;"
    allowfullscreen
  ></iframe>
</div> */}
                    </div>
             

                </div>
            </div>
        </div>
    )
}

const comparisonFn = function (prevProps, nextProps) {
  return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(NewPerformanceDashboard, comparisonFn);