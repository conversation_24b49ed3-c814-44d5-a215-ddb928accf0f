import React, { useState, useEffect, useMemo } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { Card } from "primereact/card";
import { But<PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const MSIActionItemsChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);

  // Process supply data to extract action items by category
  useEffect(() => {
    const fetchActionItemsData = async () => {
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];

        // Group by vendorCode to get latest entry per vendor
        const groupedByVendor = allAssignments.reduce((acc, item) => {
          if (!item.vendorCode) return acc;
          if (!acc[item.vendorCode]) acc[item.vendorCode] = [];
          acc[item.vendorCode].push(item);
          return acc;
        }, {});

        // Initialize counters
        let goodPractices = 0;
        let opportunityImprovement = 0;
        let regulatoryMajor = 0;
        let regulatoryMinor = 0;
        let minorNonCompliance = 0;

        Object.values(groupedByVendor).forEach(assignments => {
          // Sort to get the latest
          const latest = assignments.sort((a, b) =>
            new Date(b.created_on) - new Date(a.created_on)
          )[0];

          const actions = latest.supplierActions || [];

          actions.forEach(action => {
            switch (action.categoryOfFinding) {
              case 1:
                goodPractices++;
                break;
              case 2:
                opportunityImprovement++;
                break;
              case 3:
                // Non-compliance - check type
                switch (action.nonComplianceType) {
                  case 1:
                    regulatoryMajor++;
                    break;
                  case 2:
                    regulatoryMinor++;
                    break;
                  case 3:
                    minorNonCompliance++;
                    break;
                  default:
                    minorNonCompliance++; // Default to minor if type not specified
                    break;
                }
                break;
              default:
                break;
            }
          });
        });

        const processedData = [
          { category: "Good Practices", count: goodPractices, color: "#22C55E" },
          { category: "Opportunity for Improvement", count: opportunityImprovement, color: "#3B82F6" },
          { category: "Regulatory Major Non-compliance", count: regulatoryMajor, color: "#EF4444" },
          { category: "Regulatory Minor Non-compliance", count: regulatoryMinor, color: "#F59E0B" },
          { category: "Minor Non-compliance", count: minorNonCompliance, color: "#8B5CF6" }
        ];

        console.log('MSIActionItemsChart - Final processed data:', processedData);
        console.log('MSIActionItemsChart - Action counts:', {
          goodPractices,
          opportunityImprovement,
          regulatoryMajor,
          regulatoryMinor,
          minorNonCompliance
        });

        setChartData(processedData);
      } catch (error) {
        console.error('Error fetching action items data:', error);
        // Fallback to mock data on error
        const mockData = [
          { category: "Good Practices", count: 15, color: "#22C55E" },
          { category: "Opportunity for Improvement", count: 8, color: "#3B82F6" },
          { category: "Regulatory Major Non-compliance", count: 3, color: "#EF4444" },
          { category: "Regulatory Minor Non-compliance", count: 5, color: "#F59E0B" },
          { category: "Minor Non-compliance", count: 2, color: "#8B5CF6" }
        ];
        setChartData(mockData);
      }
    };

    fetchActionItemsData();
  }, []);

  // Highcharts configuration
  const chartOptions = useMemo(() => ({
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Arial, sans-serif'
      }
    },
    title: {
      text: 'MSI Action Items by Category',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333'
      }
    },
    xAxis: {
      categories: chartData.map(item => item.category),
      labels: {
        style: {
          fontSize: '11px',
          color: '#666'
        },
        formatter: function() {
          // Word wrap long labels
          const words = this.value.split(' ');
          if (words.length > 3) {
            const mid = Math.ceil(words.length / 2);
            return words.slice(0, mid).join(' ') + '<br/>' + words.slice(mid).join(' ');
          }
          return this.value;
        },
        useHTML: true
      },
      gridLineWidth: 0,
      lineColor: '#e0e0e0'
    },
    yAxis: {
      title: {
        text: 'Number of Actions',
        style: {
          fontSize: '12px',
          color: '#666'
        }
      },
      labels: {
        style: {
          fontSize: '11px',
          color: '#666'
        }
      },
      gridLineColor: '#f0f0f0',
      min: 0
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ccc',
      borderRadius: 8,
      shadow: true,
      style: {
        fontSize: '12px'
      },
      formatter: function() {
        return `<b>${this.x}</b><br/>Count: <b>${this.y}</b>`;
      }
    },
    plotOptions: {
      column: {
        maxPointWidth: 60,
        borderWidth: 0,
        borderRadius: 4,
        dataLabels: {
          enabled: true,
          style: {
            fontSize: '11px',
            fontWeight: 'bold',
            color: '#333'
          }
        }
      }
    },
    legend: {
      enabled: false
    },
    series: [{
      name: 'Actions',
      data: chartData.map(item => ({
        y: item.count,
        color: item.color
      })),
      colorByPoint: true
    }],
    credits: {
      enabled: false
    },
    responsive: {
      rules: [{
        condition: {
          maxWidth: 500
        },
        chartOptions: {
          plotOptions: {
            column: {
              maxPointWidth: 40
            }
          },
          xAxis: {
            labels: {
              style: {
                fontSize: '10px'
              }
            }
          }
        }
      }]
    }
  }), [chartData]);

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #007bff", paddingBottom: "5px" }}>
          Action Items by Category
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {activeMode ? (
        <div style={{ height: '400px', width: '100%' }}>
          <HighchartsReact
            highcharts={Highcharts}
            options={chartOptions}
            containerProps={{ style: { height: '100%', width: '100%' } }}
          />
        </div>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="category" header="Category" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Summary</h4>
        <div style={{ display: "flex", justifyContent: "space-between", flexWrap: "wrap" }}>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Total Actions</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#333" }}>
              {chartData.reduce((sum, item) => sum + item.count, 0)}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Non-Compliance</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#EF4444" }}>
              {chartData
                .filter(item => item.category.includes("Non-compliance"))
                .reduce((sum, item) => sum + item.count, 0)
              }
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Improvements</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#3B82F6" }}>
              {chartData.find(item => item.category === "Opportunity for Improvement")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Good Practices</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#22C55E" }}>
              {chartData.find(item => item.category === "Good Practices")?.count || 0}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MSIActionItemsChart;
