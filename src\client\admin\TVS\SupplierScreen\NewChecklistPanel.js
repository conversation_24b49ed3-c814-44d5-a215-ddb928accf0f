import React, { useState, useEffect, useCallback } from "react";

import { Tab, Row, Col, Nav, Table, Form, Modal, Card } from 'react-bootstrap';

//import {_utilities} from '../SupplierScreen';

import { Dialog } from 'primereact/dialog';
import { Checkbox } from 'primereact/checkbox';
import { Button } from 'primereact/button';
import { Steps } from 'primereact/steps';
import Swal from "sweetalert2";
import moment from 'moment'
import questionary from './assessmentform'
import { InputTextarea } from 'primereact/inputtextarea'
import { RadioButton } from "primereact/radiobutton";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { FileUpload } from "primereact/fileupload";
import APIServices from "../../../../service/APIService";
import { API } from "../../../../constants/api_url";
import { DateTime } from "luxon";
import { useSelector } from "react-redux";
import { AccordionTab, Accordion } from "primereact/accordion";
import { Tag } from "primereact/tag";
import { AttachmentAsIcon } from "../../../../components/AttachmentAsIcon";
import * as XLSX from 'xlsx';
import FileSaver from 'file-saver';
import { Calendar } from "primereact/calendar";




const NewChecklistPanel = ({ readOnly, users, auditData, updateData, vendorCode, id, submittedDate, closeModal, editable = true }) => {
    // console.log('readOnly', users)
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const [data, setData] = useState({ data1: [], id: null })
    const [formId, setFormId] = useState(null)
    const [assessmentsection, setAssessmentSection] = useState([])
    const [subsectiondata, setSubSectionData] = useState([])
    const [selectedsubsection2, setSelectedSubSection2] = useState(null)
    const [steps, setSteps] = useState([])
    const [activeIndex, setActiveIndex] = useState(0);
    const forceUpdate = useForceUpdate()
    const [headerValues, setHeaderValues] = useState({});
    const [files, setFiles] = useState([]);
    const [activeQuestionId, setActiveQuestionId] = useState(null);
    const [commentText, setCommentText] = useState('');
    const [selectedQuestion, setSelectedQuestion] = useState(null)
    const [selectSection, setSelectSection] = useState([])
    const [allCompleted, setAllCompleted] = useState(false)
    const [grandTotalScore, setGrandTotalScore] = useState(0);
    const [activeAccordionIndex, setActiveAccordionIndex] = useState(null);
    const categoryList = [{ name: 'Forging & Machining', value: 1 }, { name: 'Casting & Machining', value: 2 }, { name: 'Pressing & Fabrication', value: 3 }, { name: 'Proprietary Mechanical', value: 4 }, { name: 'Proprietary Electrical', value: 5 }, { name: 'Plastics, Rubber, Painting and Stickers', value: 6 }, { name: 'EV/3W/2W', value: 7 }, { name: 'BW', value: 8 }, { name: 'Accessories', value: 9 }, { name: 'IDM (Indirect Material)', value: 10 }, { name: 'Import', value: 11 }]

    const [draftText, setDraftText] = useState('');
    const [activeAccordion, setActiveAccordion] = useState({});
    const [document, setDocument] = useState(null)
    const [activeId, setActiveId] = useState(1); // State to hold the active framework ID
    const [labelColor, setLabelColor] = useState("#007bff"); // Default label color (black)
    const [result, setResult] = useState(null)
    const [supplierId, setSupplierId] = useState(null)
    const [submitted, setSubmitted] = useState(null)

    const [highlightedQuestion, setHighlightedQuestion] = useState(null);

    // Additional state for modal and comments
    const [showModal, setShowModal] = useState(false);
    const [comment, setComment] = useState('');

    // Use auditData as auditId for compatibility with SupplierPanel
    const auditId = auditData;

    const handleFileChange = (file) => {
        setFiles(file)
    }

    const handleComment = (question, questionIndex, sectionId, subsection1Id, subsection2Id) => {
        setSelectedQuestion({
            ...question,
            questionIndex,
            sectionId,
            subsection1Id,
            subsection2Id,
        });
        setComment(question.comment || '');
        setShowModal(true)
    }

    const uploadFilesApi = async (file, item, event) => {
        setSelectedQuestion(item)
        let promise = new Promise((resolve, rej) => {
            if (file.size <= 10000000) {
                let formData = new FormData();
                formData.append("file", file);
                APIServices.post(API.FilesUpload, formData, {
                    headers: {
                        "content-type": "multipart/form-data",
                    },
                    mode: "no-cors",
                }).then((res) => {
                    if (res.status === 200) {
                        resolve({
                            url: API.Docs + res.data.files[0].originalname,
                            name: res.data.files[0].originalname,
                            size: res.data.files[0].size,
                            type: res.data.files[0].mimetype,
                        });
                    } else {
                        rej("File upload failed");
                    }
                }).catch((err) => {
                    rej(err);
                });
            } else {
                rej("File size exceeds 10MB limit");
            }
        });
        return promise;
    };

    useEffect(() => {
        console.log("NewChecklistPanel mounted with auditData:", auditData);
        if (auditData?.id) {
            // Fetch the latest supplier audit data by ID
            fetchSupplierAuditData(auditData.id);
        } else {
            // Fallback to categories if no audit ID
            fetchCategories();
        }
    }, [auditData]);

    const fetchSupplierAuditData = async (auditId) => {
        try {
            console.log("Fetching supplier audit data for ID:", auditId);
            const response = await APIServices.get(API.SupplierAudit(auditId));
            console.log("Supplier audit data received:", response.data);

            if (response.data) {
                // Process the audit data and build assessment sections
                processAuditData(response.data);
            } else {
                // Fallback to categories if no audit data
                fetchCategories();
            }
        } catch (error) {
            console.error("Error fetching supplier audit data:", error);
            Swal.fire("Error!", "Failed to fetch supplier audit data.", "error");
            // Fallback to categories on error
            fetchCategories();
        }
    };

    const processAuditData = (auditData) => {
        // If audit data contains assessment sections, use them
        if (auditData.assessmentSections) {
            setAssessmentSection(auditData.assessmentSections);
            setSteps(auditData.assessmentSections.map(s => ({ label: s.title, id: s.id })));
        } else {
            // Otherwise, fetch categories and build sections
            fetchCategories();
        }

        // Set other audit-related data
        if (auditData.grandTotalScore) {
            setGrandTotalScore(auditData.grandTotalScore);
        }
        if (auditData.result) {
            setResult(auditData.result);
        }
        if (auditData.supplierId) {
            setSupplierId(auditData.supplierId);
        }
    };

    const fetchCategories = async () => {
        const uriString = {
            include: [
                {
                    relation: "supplySections",
                    scope: {
                        include: [{ relation: "supplyChecklists" }]
                    }
                }
            ]
        };

        try {
            const response = await APIServices.get(
                `${API.SupplyCategories}?filter=${encodeURIComponent(JSON.stringify(uriString))}`
            );
            const built = buildAssessmentSections(response.data || []);
            setAssessmentSection(built);
            setSteps(built.map(s => ({ label: s.title, id: s.id })));
            console.log("Built assessment sections:", built);

            // Auto-select first section and subsection for easier testing
            if (built.length > 0) {
                const firstSection = built[0];
                if (firstSection.assessmentSubSection1s?.length > 0) {
                    const firstSub1 = firstSection.assessmentSubSection1s[0];
                    if (firstSub1.assessmentSubSection2s?.length > 0) {
                        const firstSub2 = firstSub1.assessmentSubSection2s[0];
                        console.log("Auto-selecting:", firstSub2.id, firstSub2.title);
                        setSelectedSubSection2(firstSub2.id);
                        setData(firstSub2.form || { data1: [], id: null });
                    }
                }
            }
        } catch (error) {
            console.error("Error fetching categories:", error);
        }
    };

    // Calculate sum of scores
    const calculateSum = (data1) => {
        let sum = 0;
        try {
            const parsedData = JSON.parse(data1);
            parsedData.forEach((item) => {
                if (item.type === "radio-group") {
                    item.values.forEach((value) => {
                        if (value.selected) {
                            const numericValue = parseFloat(value.value);
                            if (!isNaN(numericValue)) {
                                sum += numericValue;
                            }
                        }
                    });
                }
            });
        } catch (error) {
            console.error("Error parsing data1:", error);
        }
        return sum;
    };

    // Check if all questions are answered
    const checkAllQuestionsAnswered = (parsedData) => {
        return parsedData.every((question) => {
            if (question.type === "radio-group" && question.values) {
                return question.values.some((v) => v.selected);
            }
            if (question.type === "textarea") {
                if (!question.value?.trim()) {
                    return false;
                }
            }
            if (question.type === "select") {
                if (!question.value) {
                    return false;
                }
            }
            return true;
        });
    };

    // Check if questionary is complete
    const checkQuestionary = (data1) => {
        if (!data1 || data1.length === 0) return false;
        try {
            const parsedData = JSON.parse(data1);
            return checkAllQuestionsAnswered(parsedData);
        } catch (error) {
            console.error("Error parsing questionary data:", error);
            return false;
        }
    };

    // Check if subsection1 is complete
    const checkSubSection1 = (subsection1) => {
        if (!subsection1.assessmentSubSection2s || subsection1.assessmentSubSection2s.length === 0) {
            return false;
        }
        return subsection1.assessmentSubSection2s.every(sub2 =>
            checkQuestionary(sub2.form?.data1 || [])
        );
    };

    // Radio button selection handler
    const onRadioButtonSelected = (
        question,
        cbind,
        auditId,
        section,
        subsection1,
        selectedsubsection2,
        formId,
        index
    ) => {
        const updatedAssessmentSection = assessmentsection.map((sectionItem) => {
            if (sectionItem.id !== section) return sectionItem;

            const updatedSubSection1s = sectionItem.assessmentSubSection1s.map(
                (subsection1Item) => {
                    if (subsection1Item.id !== subsection1) return subsection1Item;

                    const updatedSubSection2s = subsection1Item.assessmentSubSection2s.map(
                        (subsection2Item) => {
                            if (subsection2Item.id !== selectedsubsection2) return subsection2Item;

                            const parsedData = JSON.parse(subsection2Item.form.data1);
                            const updatedQuestion = { ...parsedData[index] };

                            if (updatedQuestion && updatedQuestion.values) {
                                updatedQuestion.values = updatedQuestion.values.map((value, idx) => ({
                                    ...value,
                                    selected: idx === cbind,
                                }));

                                const picked = updatedQuestion.values[cbind];
                                updatedQuestion.nc = updatedQuestion.nc ?? {
                                    raised: false, mandatory: false, action: '',
                                    dueDate: null, evidence: [], supplierId: null,
                                };

                                const reqResp = String(updatedQuestion.requiredResp || '')
                                    .trim().toUpperCase();

                                updatedQuestion.nc.raised =
                                    reqResp === 'NC' &&
                                    Number(picked.value) === 0 &&
                                    picked.label?.toUpperCase() !== 'N/A';

                                updatedQuestion.ofi.raised =
                                    updatedQuestion.category === 'other' &&
                                    ['OFI', 'CP'].includes((updatedQuestion.requiredResp || '').toUpperCase()) &&
                                    Number(picked.value) === 0 &&
                                    picked.label?.toUpperCase() !== 'N/A';

                                updatedQuestion.modified_by = vendorCode?.id;
                                updatedQuestion.modified_on = DateTime.utc();
                            }

                            parsedData[index] = updatedQuestion;
                            const newScore = calculateSum(JSON.stringify(parsedData));
                            const allAnswered = checkAllQuestionsAnswered(parsedData);

                            return {
                                ...subsection2Item,
                                completed: allAnswered,
                                form: {
                                    ...subsection2Item.form,
                                    data1: JSON.stringify(parsedData),
                                    score: newScore,
                                },
                            };
                        }
                    );

                    const totalScoreForSubsection1 = updatedSubSection2s.reduce((acc, sub2) => {
                        return acc + (sub2.form?.score || 0);
                    }, 0);

                    const allSub2Completed =
                        updatedSubSection2s.length > 0 &&
                        updatedSubSection2s.every((sub2) => sub2.completed);

                    return {
                        ...subsection1Item,
                        assessmentSubSection2s: updatedSubSection2s,
                        totalScore: totalScoreForSubsection1,
                        totalCompleted: allSub2Completed,
                    };
                }
            );

            const allSub1Completed =
                updatedSubSection1s.length > 0 &&
                updatedSubSection1s.every((sub1) => sub1.totalCompleted);

            const totalScoreForSection = updatedSubSection1s.reduce((acc, sub1) => {
                return acc + (sub1.totalScore || 0);
            }, 0);

            return {
                ...sectionItem,
                assessmentSubSection1s: updatedSubSection1s,
                sectionTotalScore: totalScoreForSection,
                completed: allSub1Completed,
            };
        });

        setAssessmentSection(updatedAssessmentSection);

        const overallScore = updatedAssessmentSection.reduce((acc, sec) => {
            return acc + (sec.sectionTotalScore || 0);
        }, 0);

        setGrandTotalScore(overallScore);

        const isEverythingCompleted =
            updatedAssessmentSection.length > 0 &&
            updatedAssessmentSection.every((sec) => sec.completed === true);

        setAllCompleted(isEverythingCompleted);
    };

    // Textarea change handler
    const onTextareaChange = (newText, questionIndex, sectionId, subsection1Id, subsection2Id) => {
        const updatedAssessmentSection = assessmentsection.map((sectionItem) => {
            if (sectionItem.id !== sectionId) return sectionItem;

            const updatedSubSection1s = sectionItem.assessmentSubSection1s.map((subsection1Item) => {
                if (subsection1Item.id !== subsection1Id) return subsection1Item;

                const updatedSubSection2s = subsection1Item.assessmentSubSection2s.map((subsection2Item) => {
                    if (subsection2Item.id !== subsection2Id) return subsection2Item;

                    const parsedData = JSON.parse(subsection2Item.form.data1);
                    parsedData[questionIndex].value = newText;
                    parsedData[questionIndex].modified_by = vendorCode?.id;
                    parsedData[questionIndex].modified_on = DateTime.utc();

                    const allAnswered = checkAllQuestionsAnswered(parsedData);

                    return {
                        ...subsection2Item,
                        completed: allAnswered,
                        form: {
                            ...subsection2Item.form,
                            data1: JSON.stringify(parsedData),
                        },
                    };
                });

                return {
                    ...subsection1Item,
                    assessmentSubSection2s: updatedSubSection2s,
                };
            });

            return {
                ...sectionItem,
                assessmentSubSection1s: updatedSubSection1s,
            };
        });

        setAssessmentSection(updatedAssessmentSection);
    };

    // Dropdown change handler
    const onChangeDropwdown = (newValue, questionIndex, sectionId, subsection1Id, subsection2Id) => {
        const updatedAssessmentSection = assessmentsection.map((sectionItem) => {
            if (sectionItem.id !== sectionId) return sectionItem;

            const updatedSubSection1s = sectionItem.assessmentSubSection1s.map((subsection1Item) => {
                if (subsection1Item.id !== subsection1Id) return subsection1Item;

                const updatedSubSection2s = subsection1Item.assessmentSubSection2s.map((subsection2Item) => {
                    if (subsection2Item.id !== subsection2Id) return subsection2Item;

                    const parsedData = JSON.parse(subsection2Item.form.data1);
                    parsedData[questionIndex].value = newValue;
                    parsedData[questionIndex].modified_by = vendorCode?.id;
                    parsedData[questionIndex].modified_on = DateTime.utc();

                    const allAnswered = checkAllQuestionsAnswered(parsedData);

                    return {
                        ...subsection2Item,
                        completed: allAnswered,
                        form: {
                            ...subsection2Item.form,
                            data1: JSON.stringify(parsedData),
                        },
                    };
                });

                return {
                    ...subsection1Item,
                    assessmentSubSection2s: updatedSubSection2s,
                };
            });

            return {
                ...sectionItem,
                assessmentSubSection1s: updatedSubSection1s,
            };
        });

        setAssessmentSection(updatedAssessmentSection);
    };

    // Handle save comment
    const handleSaveComment = () => {
        if (!selectedQuestion) return;

        const { questionIndex, sectionId, subsection1Id, subsection2Id } = selectedQuestion;

        const updatedAssessmentSection = assessmentsection.map((sectionItem) => {
            if (sectionItem.id !== sectionId) return sectionItem;

            const updatedSubSection1s = sectionItem.assessmentSubSection1s.map((subsection1Item) => {
                if (subsection1Item.id !== subsection1Id) return subsection1Item;

                const updatedSubSection2s = subsection1Item.assessmentSubSection2s.map((subsection2Item) => {
                    if (subsection2Item.id !== subsection2Id) return subsection2Item;

                    const parsedData = JSON.parse(subsection2Item.form.data1);
                    parsedData[questionIndex].comment = comment;
                    parsedData[questionIndex].modified_by = vendorCode?.id;
                    parsedData[questionIndex].modified_on = DateTime.utc();

                    return {
                        ...subsection2Item,
                        form: {
                            ...subsection2Item.form,
                            data1: JSON.stringify(parsedData),
                        },
                    };
                });

                return {
                    ...subsection1Item,
                    assessmentSubSection2s: updatedSubSection2s,
                };
            });

            return {
                ...sectionItem,
                assessmentSubSection1s: updatedSubSection1s,
            };
        });

        setAssessmentSection(updatedAssessmentSection);
        setShowModal(false);
        setSelectedQuestion(null);
        setComment('');
    };

    // Handle close modal
    const handleCloseModal = () => {
        setShowModal(false);
        setSelectedQuestion(null);
        setComment('');
    };

    // Question rendering function
    const renderQuestion = (question, index, section, subsection1, selectedsubsection2) => {
        const highlightThis = highlightedQuestion === `${section}-${subsection1}-${selectedsubsection2}-${index}`;

        return (
            <div key={`question-${section}-${subsection1}-${selectedsubsection2}-${index}`}
                 className='questDisp col-12 grid m-0 p-0'
                 style={{
                     border: highlightThis ? "2px solid red" : "none",
                     padding: highlightThis ? "8px" : "0"
                 }}>
                <div className="col-10 fs-16 fw-4">
                    <p style={{ color: question.isDedicated ? "red" : '#374151' }}>
                        {question.label}
                        {question.tag && (
                            <span className={`badge ms-2 ${question.tag}`}>
                                {question.tag.toUpperCase()}
                            </span>
                        )}
                    </p>

                    {question.type === 'radio-group' ? (
                        <div className='grid m-0 p-0'>
                            {question.values.map((cb, cbind) => {
                                return (
                                    <div key={cbind} className="p-2 flex text-justify fs-14 fw-5 align-items-center">
                                        <RadioButton
                                            disabled={!editable}
                                            name={`q-${section}-${subsection1}-${selectedsubsection2}-${index}`}
                                            inputId={`rg-${section}-${subsection1}-${selectedsubsection2}-${index}-${cbind}`}
                                            value={cb.value}
                                            onChange={() =>
                                                onRadioButtonSelected(
                                                    question,
                                                    cbind,
                                                    auditId,
                                                    section,
                                                    subsection1,
                                                    selectedsubsection2,
                                                    formId,
                                                    index
                                                )
                                            }
                                            checked={cb.selected || false}
                                        />
                                        <label htmlFor={`rg-${section}-${subsection1}-${selectedsubsection2}-${index}-${cbind}`}
                                               className="ml-2">
                                            {cb.label}
                                        </label>
                                    </div>
                                )
                            })}
                        </div>
                    ) : question.type === 'select' ? (
                        <Dropdown
                            disabled={!editable}
                            placeholder={question.placeholder}
                            options={question.values}
                            style={{ width: '100%' }}
                            optionLabel='label'
                            optionValue="value"
                            value={question.value}
                            onChange={(e) => {
                                onChangeDropwdown(e.value, index, section, subsection1, selectedsubsection2)
                            }}
                        />
                    ) : question.type === 'textarea' ? (
                        <InputTextarea
                            key={`textarea-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}`}
                            disabled={!editable}
                            placeholder="Enter your response here"
                            value={question.value || ''}
                            style={{ width: '100%', height: 120, overflow: 'auto' }}
                            onChange={(e) => {
                                onTextareaChange(
                                    e.target.value,
                                    index,
                                    section,
                                    subsection1,
                                    selectedsubsection2
                                );
                            }}
                        />
                    ) : null}
                </div>

                <div className="col-2 flex justify-content-between" style={{ flexDirection: 'column' }}>
                    {question.isAttachmentMandatory && editable && (
                        <div>
                            <FileUpload
                                mode="basic"
                                name="demo[]"
                                url="/api/upload"
                                accept="image/*,.pdf,.doc,.docx"
                                maxFileSize={10000000}
                                onSelect={(e) => {
                                    const file = e.files[0];
                                    if (file) {
                                        uploadFilesApi(file, question).then((uploadedFile) => {
                                            // Handle uploaded file
                                            console.log("File uploaded:", uploadedFile);
                                        }).catch((error) => {
                                            console.error("Upload error:", error);
                                        });
                                    }
                                }}
                                chooseLabel="Upload"
                                className="p-button-sm"
                            />
                        </div>
                    )}

                    {editable ? (
                        <div style={{ color: '#315975' }}
                             className=""
                             onClick={(e) => {
                                 e.stopPropagation();
                                 handleComment(question, index, section, subsection1, selectedsubsection2);
                             }}>
                            <i className="pi pi-comment fs-14 fw-6 mr-2"
                               style={{ marginRight: '5px', cursor: 'pointer' }}></i>
                            <span>{question.comment ? 'Update ' : 'Add '} Comment</span>
                        </div>
                    ) : (
                        !editable && question.comment && (
                            <div style={{ color: '#315975' }}
                                 className=""
                                 onClick={(e) => {
                                     e.stopPropagation();
                                     handleComment(question, index, section, subsection1, selectedsubsection2);
                                 }}>
                                <i className="pi pi-comment fs-14 fw-6 mr-2"
                                   style={{ marginRight: '5px', cursor: 'pointer' }}></i>
                                <span>View Comment</span>
                            </div>
                        )
                    )}

                    <div className="mt-2">
                        <AttachmentAsIcon
                            mandatory={false}
                            edit={editable ? 1 : 0}
                            documents={question.attachments}
                            refId={`OPT_${section}_${subsection1}_${selectedsubsection2}_${index}`}
                            onDocumentChange={(newDocs) => {
                                // Handle document changes
                                console.log("Documents changed:", newDocs);
                            }}
                        />
                    </div>
                </div>

                <hr className="p-1 m-0" />
            </div>
        );
    };

    // Excel export functionality
    const exportToExcel = () => {
        const exportData = [];

        assessmentsection.forEach((section) => {
            section.assessmentSubSection1s?.forEach((subsection1) => {
                subsection1.assessmentSubSection2s?.forEach((subsection2) => {
                    try {
                        const questions = JSON.parse(subsection2.form?.data1 || '[]');
                        questions.forEach((question, index) => {
                            const selectedValue = question.values?.find(v => v.selected);
                            exportData.push({
                                'Section': section.title,
                                'Sub-Section 1': subsection1.title,
                                'Sub-Section 2': subsection2.title,
                                'Question Number': question.questionNumber || index + 1,
                                'Question': question.label,
                                'Type': question.type,
                                'Answer': selectedValue?.label || question.value || 'Not Answered',
                                'Score': selectedValue?.value || 0,
                                'Comment': question.comment || '',
                                'Category': question.category,
                                'Required Response': question.requiredResp || '',
                                'Mandatory': question.mandatory ? 'Yes' : 'No'
                            });
                        });
                    } catch (error) {
                        console.error("Error parsing questions for export:", error);
                    }
                });
            });
        });

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Assessment Data');

        const fileName = `Supplier_Audit_${auditData?.id || 'Unknown'}_${DateTime.now().toFormat('yyyy-MM-dd')}.xlsx`;
        XLSX.writeFile(workbook, fileName);
    };

    /**
     * Convert the SupplyCategories response → assessmentsection[]
     */
    /* ------------------------------------------------------------------ */
    /* helpers                                                             */
    /* ------------------------------------------------------------------ */
    const translateType = (src) => {
        switch (src) {
            case 'A':                       // Yes / No / NA
            case 'B':
            case 'C': return 'radio-group'; // ← C is now multi-choice
            case 'D': return 'textarea';
            case 'E': return 'attachment';
            default: return 'radio-group';
        }
    };
    const buildRadioValues = (q) => {
        /* A & B keep the Yes / No ( / N-A ) pattern */
        if (['A', 'B'].includes(q.type)) {
            return [
                { label: 'Yes', value: q.yesScore ?? 1, selected: false },
                { label: 'No', value: q.noScore ?? 0, selected: false },
                ...(q.naScore != null
                    ? [{ label: 'N/A', value: q.naScore, selected: false }]
                    : []),
            ];
        }

        /* NEW — type C: parse ">50% = 1, 100% = 2, …" */
        if (q.type === 'C' && typeof q.marks === 'string') {
            return q.marks
                .split(',')
                .map(s => s.trim())
                .filter(Boolean)
                .map((pair) => {
                    const [left, right] = pair.split('=');
                    return {
                        label: (left ?? '').trim(),              // e.g. ">50%"
                        value: Number((right ?? '').trim()) || 0, // e.g. 1
                        selected: false,
                    };
                });
        }

        /* everything else (D) has no predefined choices */
        return [];
    };
    const tagFromCategory = (cat) => cat === 'other' ? 'oc' : 'mc';
    const makeQuestionObject = (q) => ({
        /* --- UI-facing fields --- */
        id: q.id,
        label: q.text,
        type: translateType(q.type),
        placeholder: '',
        values: buildRadioValues(q),
        value: '',
        comment: '',
        attachments: [],
        mandatory: q.category === 'mandatory',
        isAttachmentMandatory: q.type === 'E' && q.category === 'mandatory',
        tag: tagFromCategory(q.category),
        /* --- keep everything else for later use --- */
        raw: { ...q },               // <— full backend blob

        /* (or expose individually if you prefer) */
        questionNumber: q.questionNumber,
        numerator: q.numerator,
        denominator: q.denominator,
        yesScore: q.yesScore,
        noScore: q.noScore,
        naScore: q.naScore,
        marksHint: q.marks,
        requiredResp: q.requiredResponse,
        category: q.category,
        order: q.order ?? 0,
        nc: {
            raised: false,                               // will flip to true
            mandatory: q.requiredResponse === 'NC'          // ↳ "MC + NC"  ⇒ must be filled
                && q.category !== 'other',          // ↳ "OC + NC"  ⇒ optional
            action: '',
            dueDate: null,
            evidence: [],                                  // same shape as attachments
            supplierId: null,
        },
        ofi: {                         // ⭐ NEW  ⭐
            raised: false,               // flips to true when score 0 selected
            findings: '',                // "Findings"
            improvement: '',             // "Opportunity for Improvement"
            evidence: [],                // attachments (same shape as nc.evidence)
        },
    });
    /* ------------------------------------------------------------------ */
    /* main builder                                                        */
    /* ------------------------------------------------------------------ */
    function buildAssessmentSections(categories) {
        return categories.map((cat, catIdx) => ({
            id: cat.id,
            title: cat.name,
            order: catIdx,
            completed: false,
            sectionTotalScore: 0,

            /* supplySections → SubSection-1 */
            assessmentSubSection1s: cat.supplySections.map((sec, secIdx) => ({
                id: sec.id,
                title: sec.name,
                order: secIdx,
                totalScore: 0,
                totalCompleted: false,

                /* supplyChecklists → SubSection-2 */
                assessmentSubSection2s: sec.supplyChecklists.map((cl, clIdx) => {
                    const parsed = JSON.parse(cl.values);                  // checklist JSON
                    const questions = parsed.components?.[0]?.questions ?? [];
                    const qObjects = questions.map(makeQuestionObject);

                    return {
                        id: cl.id,
                        title: cl.name,
                        order: clIdx,
                        completed: false,
                        form: {
                            data1: JSON.stringify(qObjects),   // ← what your editor consumes
                            score: 0,
                        },
                    };
                }),
            })),
        }));
    }

    const handleStepClick = (sec, index) => {
        setActiveIndex(index)
        setSubSectionData(assessmentsection.find(i => i.id === sec.id))
    };

    const [expandedItems, setExpandedItems] = useState([]); // Track expanded items
    const [expandAll, setExpandAll] = useState(false); // State to toggle expand all items

    // Create steps for stepper (same format as SupplierPanel)
    const stepss = steps.map((step, index) => ({
        ...step,
        completed: assessmentsection[index]?.completed || false,
        status: assessmentsection[index]?.completed ? 1 : 0
    }));

    const getDate = (date, format) => {
        if (!date) {
            return 'Not Set'
        }
        if (typeof date === 'string') {
            return DateTime.fromISO(date, { zone: 'utc' }).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        } else if (DateTime.isDateTime(date)) {
            return date.toFormat(format ? format : 'dd-MM-yyyy')
        } else {
            return DateTime.fromJSDate(date).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        }
    };

    // Handle save progress functionality
    const handleProgress = () => {
        console.log("Save Progress clicked for audit:", auditData);
        console.log("Assessment sections:", assessmentsection);

        if (auditData?.id) {
            const requestBody = {
                auditId: auditData.id,
                assessmentData: JSON.stringify(assessmentsection),
                status: 1, // Save progress status
                grandTotalScore: grandTotalScore,
                modified_on: DateTime.utc(),
                modified_by: vendorCode?.id || login_data.id,
            };

            // Call supplier-audit API with status 1 for save progress
            APIServices.post(API.SupplierAudits, requestBody)
                .then(response => {
                    console.log("Progress saved successfully:", response);
                    Swal.fire("Success!", "Progress saved successfully.", "success");
                })
                .catch(error => {
                    console.error("Error saving progress:", error);
                    Swal.fire("Error!", "Failed to save progress.", "error");
                });
        }
    };

    // Handle submit functionality
    const handleSubmit = () => {
        console.log("Submit clicked for audit:", auditData);
        console.log("Assessment sections:", assessmentsection);

        if (auditData?.id) {
            const requestBody = {
                auditId: auditData.id,
                assessmentData: JSON.stringify(assessmentsection),
                status: 2, // Submit status
                grandTotalScore: grandTotalScore,
                submitted_on: DateTime.utc(),
                submitted_by: vendorCode?.id || login_data.id,
                modified_on: DateTime.utc(),
                modified_by: vendorCode?.id || login_data.id,
            };

            // Call supplier-audit API with status 2 for submit
            APIServices.post(API.SupplierAudits, requestBody)
                .then(response => {
                    console.log("Submitted successfully:", response);
                    Swal.fire("Success!", "Audit submitted successfully.", "success").then(() => {
                        closeModal(false);
                    });
                })
                .catch(error => {
                    console.error("Error submitting:", error);
                    Swal.fire("Error!", "Failed to submit audit.", "error");
                });
        }
    };

    return (
        <>
            <div className="row mb-3" style={{ padding: '10px' }}>
                <div className="col-12 d-flex justify-content-end mb-3">
                    <Button
                        label="Download Excel"
                        icon="pi pi-file-excel"
                        className="p-button-success"
                        onClick={exportToExcel}
                    />
                </div>

                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Audit ID</p>
                    <p className="obs-content">
                        {auditData?.id || 'N/A'}
                    </p>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Supplier Name</p>
                    <p className="obs-content">
                        {auditData?.supplierName || 'N/A'}
                    </p>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Status</p>
                    <Tag style={{ width: 'unset' }}
                         className={auditData?.status === 'Completed' ? 'status-tag-green' :
                                   auditData?.status === 'In Progress' ? 'status-tag-orange' :
                                   'status-tag-red'}>
                        {auditData?.status || 'N/A'}
                    </Tag>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Audit Type</p>
                    <p className="obs-content">
                        {auditData?.auditType || 'N/A'}
                    </p>
                </div>

                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Created Date</p>
                    <p className="obs-content">
                        {getDate(auditData?.createdDate)}
                    </p>
                </div>

                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">MSI Self-Assessment Score</p>
                    <p className="obs-content">
                        {grandTotalScore}
                    </p>
                </div>
            </div>

            <Tab.Container id="left-tabs-example" className="mt-3 audit-tab" activeKey={assessmentsection?.[activeIndex]?.id}>
                <Row style={{ marginTop: 20 }}>
                    <div style={{ width: '90%', margin: '0 auto' }}>
                        <Stepper
                            editable={editable}
                            steps={stepss}
                            onClickStep={handleStepClick}
                            activeBGColor="green"
                            activeIndex={activeIndex}
                            labelColor={labelColor}
                        />
                    </div>

                    <Tab.Content>
                        {assessmentsection.map((section, index) => {
                            return (
                                <Tab.Pane eventKey={section.id} key={section.id}>
                                    <label className="mb-4" style={{ fontSize: 14, fontWeight: 'bold' }}>
                                        Expand each of the sections below, click on the sub-section and provide your responses to all the check points. Where required, attach relevant documents. Please note that check points in red are considered critical questions from TVS Motors perspective and require documentary evidence.
                                    </label>

                                    <Accordion
                                        activeIndex={activeAccordion[section.id] || null}
                                        onTabChange={(e) => {
                                            setActiveAccordion(prev => ({ ...prev, [section.id]: e.index }))
                                        }}
                                    >
                                        {section?.assessmentSubSection1s?.sort((a, b) => a.order - b.order).map((subsection1) => {
                                            return (
                                                <AccordionTab
                                                    key={subsection1.id}
                                                    header={`${subsection1.title} - ${subsection1.totalScore || 0}`}
                                                    headerStyle={{ borderLeft: '5px solid ' + (subsection1.totalCompleted ? 'green' : '#F59E0B') }}
                                                    onTabOpen={() => {
                                                        // Auto-select first sub2 when accordion opens
                                                        if (subsection1.assessmentSubSection2s?.length > 0) {
                                                            const firstSub2 = subsection1.assessmentSubSection2s[0];
                                                            console.log("Auto-selecting first tab:", firstSub2.id, firstSub2.title);
                                                            setSelectedSubSection2(firstSub2.id);
                                                            setData(firstSub2.form || { data1: [], id: null });
                                                        }
                                                    }}
                                                >
                                                    <Nav variant="pills" className="flex-row custom-nav">
                                                        {subsection1.assessmentSubSection2s
                                                            .sort((a, b) => a.order - b.order)
                                                            .map(sub2 => (
                                                                <Nav.Item key={sub2.id}>
                                                                    <Nav.Link
                                                                        eventKey={sub2.id}
                                                                        active={selectedsubsection2 === sub2.id}
                                                                        style={{
                                                                            borderLeft: `5px solid ${sub2.completed ? 'green' : '#F59E0B'}`,
                                                                        }}
                                                                        onClick={() => {
                                                                            console.log("Tab clicked:", sub2.id, sub2.title);
                                                                            setSelectedSubSection2(sub2.id);
                                                                            setData(sub2.form || { data1: [], id: null });
                                                                        }}
                                                                    >
                                                                        {sub2.title}
                                                                    </Nav.Link>
                                                                </Nav.Item>
                                                            ))}
                                                    </Nav>

                                                    <Tab.Content className="mt-3">
                                                        {subsection1.assessmentSubSection2s.map(sub2 => (
                                                            <Tab.Pane eventKey={sub2.id} key={sub2.id}>
                                                                {selectedsubsection2 === sub2.id && (
                                                                    <div>
                                                                        <h5>{sub2.title}</h5>
                                                                        {(() => {
                                                                            try {
                                                                                console.log("Rendering questions for sub2:", sub2.id, sub2.title);
                                                                                console.log("Form data1:", sub2.form?.data1);
                                                                                const parsedData = JSON.parse(sub2.form?.data1 || '[]');
                                                                                console.log("Parsed questions:", parsedData);

                                                                                if (!parsedData || parsedData.length === 0) {
                                                                                    // Create sample questions for testing
                                                                                    const sampleQuestions = [
                                                                                        {
                                                                                            id: 1,
                                                                                            label: "Sample Question 1: Is the quality management system documented?",
                                                                                            type: "radio-group",
                                                                                            values: [
                                                                                                { label: "Yes", value: 1, selected: false },
                                                                                                { label: "No", value: 0, selected: false },
                                                                                                { label: "N/A", value: 0, selected: false }
                                                                                            ],
                                                                                            comment: "",
                                                                                            attachments: [],
                                                                                            mandatory: true,
                                                                                            tag: "mc"
                                                                                        },
                                                                                        {
                                                                                            id: 2,
                                                                                            label: "Sample Question 2: Describe the quality control process",
                                                                                            type: "textarea",
                                                                                            value: "",
                                                                                            comment: "",
                                                                                            attachments: [],
                                                                                            mandatory: false,
                                                                                            tag: "oc"
                                                                                        }
                                                                                    ];

                                                                                    return (
                                                                                        <div>
                                                                                            <p style={{ color: 'orange' }}>Using sample questions (no data found)</p>
                                                                                            {sampleQuestions.map((question, qIndex) => {
                                                                                                console.log("Rendering sample question:", qIndex, question);
                                                                                                return renderQuestion(
                                                                                                    question,
                                                                                                    qIndex,
                                                                                                    section.id,
                                                                                                    subsection1.id,
                                                                                                    sub2.id
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    );
                                                                                }

                                                                                return parsedData.map((question, qIndex) => {
                                                                                    console.log("Rendering question:", qIndex, question);
                                                                                    return renderQuestion(
                                                                                        question,
                                                                                        qIndex,
                                                                                        section.id,
                                                                                        subsection1.id,
                                                                                        sub2.id
                                                                                    );
                                                                                });
                                                                            } catch (error) {
                                                                                console.error("Error parsing questions:", error);
                                                                                console.error("Raw data1:", sub2.form?.data1);
                                                                                return <p>Error loading questions: {error.message}</p>;
                                                                            }
                                                                        })()}
                                                                    </div>
                                                                )}
                                                            </Tab.Pane>
                                                        ))}
                                                    </Tab.Content>
                                                </AccordionTab>
                                            );
                                        })}
                                    </Accordion>
                                </Tab.Pane>
                            );
                        })}
                    </Tab.Content>
                </Row>
            </Tab.Container>

            {!submitted && assessmentsection?.length && editable && (
                <div className="mt-4" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
                    <Button
                        label="Save Progress"
                        className="p-button-warning"
                        onClick={handleProgress}
                    />
                    <Button
                        label="Submit"
                        className="p-button-success"
                        onClick={handleSubmit}
                    />
                </div>
            )}

            {/* Comment Modal */}
            <Dialog
                header="Add Comment"
                visible={showModal}
                style={{ width: '75%' }}
                onHide={handleCloseModal}
            >
                <div>
                    <InputTextarea
                        value={comment}
                        disabled={!editable}
                        onChange={(e) => setComment(e.target.value)}
                        style={{ width: '100%', height: 150, overflow: 'auto' }}
                    />
                    <div className="flex justify-content-end col-12">
                        <Button
                            style={{ width: 110 }}
                            className="mr-2"
                            label="Close"
                            onClick={() => {
                                setShowModal(false);
                            }}
                        />
                        {editable && (
                            <Button
                                style={{ width: 110 }}
                                label="Save & Exit"
                                onClick={() => handleSaveComment()}
                            />
                        )}
                    </div>
                </div>
            </Dialog>
        </>
    );
};

// Stepper component (copied from SupplierPanel)
const Stepper = ({ editable, steps, onClickStep, labelColor = 'black', activeBGColor = '#6c757d', activeIndex = 0 }) => {
    return (
        <>
            <div style={{ display: 'flex', justifyContent: 'center' }}>
                {steps.map((step, index) => (
                    <React.Fragment key={index}>
                        <div style={{ position: 'relative' }}>
                            <div
                                onClick={() => onClickStep && onClickStep(step, index)}
                                style={{
                                    display: 'flex',
                                    position: 'relative',
                                    flexDirection: 'column',
                                    zIndex: 1,
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    textAlign: 'center',
                                    width: '170px', // Fixed width to keep all steps aligned
                                }}
                            >
                                <div
                                    style={{
                                        backgroundColor: step.completed === undefined
                                            ? 'grey'
                                            : step.completed === true
                                                ? 'green'
                                                : step.completed === false
                                                    ? '#F59E0B'
                                                    : 'grey',
                                        color: 'white',
                                        borderRadius: '50%',
                                        width: '30px',
                                        height: '30px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        fontWeight: 'bold',
                                        fontSize: '14px',
                                    }}
                                >
                                    {index + 1} {/* Display step number starting from 1 */}
                                </div>
                                <div
                                    style={{
                                        fontSize: 16,
                                        marginTop: '8px',
                                        fontWeight: activeIndex === index ? 700 : 'normal',
                                        color: activeIndex === index ? labelColor : 'black',
                                        textDecoration: activeIndex === index ? 'underline' : 'none',
                                    }}
                                >
                                    {step.label}
                                </div>
                            </div>

                            {/* Add line between steps, except for the last step */}
                            {index < steps.length - 1 && (
                                <hr
                                    style={{
                                        alignItems: 'center',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        left: '50%',
                                        top: '8px',
                                        position: 'absolute',
                                        width: '100px',
                                        zIndex: 0,
                                    }}
                                />
                            )}
                        </div>
                    </React.Fragment>
                ))}
            </div>

            {editable && (
                <div className='color mb-3 mt-3 d-flex justify-content-end'>
                    <div className="d-flex align-items-center me-4">
                        <div className="instruction-circle" style={{ backgroundColor: '#28a745' }}></div>
                        <span className="ms-2">Finalized</span>
                    </div>
                    <div className="d-flex align-items-center me-4">
                        <div className="instruction-circle" style={{ backgroundColor: '#ffa500' }}></div>
                        <span className="ms-2">Drafted</span>
                    </div>
                    <div className="d-flex align-items-center">
                        <div className="instruction-circle" style={{ backgroundColor: '#808080' }}></div>
                        <span className="ms-2">No information entered</span>
                    </div>
                </div>
            )}
        </>
    );
};

export default NewChecklistPanel;
